#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TPDOG版块监控程序启动脚本
提供简单的启动界面和选项
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查.env文件
    if not os.path.exists('.env'):
        print("❌ 未找到.env文件")
        print("   请创建.env文件并配置TPDOG_TOKEN")
        return False
    
    # 加载环境变量
    load_dotenv()
    tpdog_token = os.getenv('TPDOG_TOKEN')
    
    if not tpdog_token:
        print("❌ TPDOG_TOKEN未配置")
        print("   请在.env文件中设置: TPDOG_TOKEN=your_token")
        return False
    
    print(f"✅ TPDOG_TOKEN已配置: {tpdog_token[:10]}...")
    
    # 检查必要的Python包
    try:
        import requests
        import pandas as pd
        import schedule
        print("✅ 必要的Python包已安装")
    except ImportError as e:
        print(f"❌ 缺少必要的Python包: {e}")
        print("   请运行: pip install requests pandas schedule python-dotenv")
        return False
    
    return True

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("🚀 TPDOG版块资金流监控程序")
    print("=" * 60)
    print("请选择操作:")
    print("1. 运行测试程序（验证功能）")
    print("2. 启动监控程序（正式运行）")
    print("3. 手动获取版块列表")
    print("4. 手动获取版块资金流（仅交易时间）")
    print("5. 查看今日数据文件")
    print("0. 退出")
    print("=" * 60)

def run_test():
    """运行测试程序"""
    print("\n🔄 启动测试程序...")
    os.system("python test_tpdog_monitor.py")

def run_monitor():
    """启动监控程序"""
    print("\n🔄 启动监控程序...")
    print("⚠️ 程序将持续运行，按Ctrl+C停止")
    input("按回车键继续...")
    
    try:
        from tpdog_sector_monitor import main
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")

def manual_get_lists():
    """手动获取版块列表"""
    print("\n🔄 手动获取版块列表...")
    try:
        from tpdog_sector_monitor import check_and_update_sector_lists
        check_and_update_sector_lists()
        print("✅ 版块列表获取完成")
    except Exception as e:
        print(f"❌ 获取版块列表失败: {e}")

def manual_get_funds():
    """手动获取版块资金流"""
    print("\n🔄 手动获取版块资金流...")
    try:
        from tpdog_sector_monitor import get_sector_funds_data, is_trading_time
        
        if not is_trading_time():
            print("⚠️ 当前非交易时间，资金流数据可能为空")
            choice = input("是否继续？(y/N): ")
            if choice.lower() != 'y':
                return
        
        get_sector_funds_data()
        print("✅ 版块资金流获取完成")
    except Exception as e:
        print(f"❌ 获取版块资金流失败: {e}")

def view_data_files():
    """查看今日数据文件"""
    print("\n📁 查看今日数据文件...")
    
    today_str = datetime.now().strftime('%Y%m%d')
    data_folder = os.path.join('data', today_str)
    
    if not os.path.exists(data_folder):
        print(f"⚠️ 今日数据文件夹不存在: {data_folder}")
        return
    
    files = os.listdir(data_folder)
    tpdog_files = [f for f in files if 'tpdog' in f.lower()]
    
    if not tpdog_files:
        print("⚠️ 未找到TPDOG数据文件")
        return
    
    print(f"📂 数据文件夹: {data_folder}")
    print(f"📄 找到 {len(tpdog_files)} 个TPDOG数据文件:")
    
    for i, file in enumerate(tpdog_files, 1):
        file_path = os.path.join(data_folder, file)
        file_size = os.path.getsize(file_path)
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        
        print(f"  {i:2d}. {file}")
        print(f"      大小: {file_size:,} 字节")
        print(f"      修改时间: {file_time.strftime('%H:%M:%S')}")
        
        # 如果是CSV文件，显示行数
        if file.endswith('.csv'):
            try:
                import pandas as pd
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                print(f"      记录数: {len(df)} 条")
            except:
                pass
        print()

def main():
    """主函数"""
    print("TPDOG版块监控程序启动器")
    print("启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，程序退出")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选项 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                run_test()
            elif choice == '2':
                run_monitor()
            elif choice == '3':
                manual_get_lists()
            elif choice == '4':
                manual_get_funds()
            elif choice == '5':
                view_data_files()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"\n❌ 操作失败: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
