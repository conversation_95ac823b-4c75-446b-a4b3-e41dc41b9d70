#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TPDOG版块监控程序启动脚本
检查环境并直接启动监控程序
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查.env文件
    if not os.path.exists('.env'):
        print("❌ 未找到.env文件")
        print("   请创建.env文件并配置TPDOG_TOKEN")
        return False
    
    # 加载环境变量
    load_dotenv()
    tpdog_token = os.getenv('TPDOG_TOKEN')
    
    if not tpdog_token:
        print("❌ TPDOG_TOKEN未配置")
        print("   请在.env文件中设置: TPDOG_TOKEN=your_token")
        return False
    
    print(f"✅ TPDOG_TOKEN已配置: {tpdog_token[:10]}...")
    
    # 检查必要的Python包
    try:
        import requests
        import pandas as pd
        import schedule
        print("✅ 必要的Python包已安装")
    except ImportError as e:
        print(f"❌ 缺少必要的Python包: {e}")
        print("   请运行: pip install requests pandas schedule python-dotenv")
        return False
    
    return True

def run_monitor():
    """启动监控程序"""
    print("\n🔄 启动监控程序...")
    print("⚠️ 程序将持续运行，按Ctrl+C停止")

    try:
        from tpdog_sector_monitor import main
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")

def main():
    """主函数"""
    print("TPDOG版块监控程序启动器")
    print("启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，程序退出")
        return

    # 直接启动监控程序
    run_monitor()

if __name__ == "__main__":
    main()
