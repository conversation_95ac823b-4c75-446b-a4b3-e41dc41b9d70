#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TPDOG版块监控程序
验证各个功能模块是否正常工作
"""

import os
import sys
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入TPDOG监控模块
from tpdog_sector_monitor import (
    get_tpdog_sector_list,
    get_tpdog_sector_funds,
    get_tpdog_sector_stocks,
    get_tpdog_sector_quote,
    is_trading_time,
    format_amount
)

def test_env_config():
    """测试环境配置"""
    print("=" * 50)
    print("测试1: 环境配置检查")
    print("=" * 50)
    
    load_dotenv()
    tpdog_token = os.getenv('TPDOG_TOKEN')
    
    if tpdog_token:
        print(f"✅ TPDOG_TOKEN已配置: {tpdog_token[:10]}...")
        return tpdog_token
    else:
        print("❌ TPDOG_TOKEN未配置")
        return None

def test_trading_time():
    """测试交易时间判断"""
    print("\n" + "=" * 50)
    print("测试2: 交易时间判断")
    print("=" * 50)
    
    current_time = datetime.now().strftime('%H:%M:%S')
    is_trading = is_trading_time()
    
    print(f"当前时间: {current_time}")
    print(f"是否交易时间: {'是' if is_trading else '否'}")
    print("交易时间: 09:30-11:30 / 13:00-15:00")
    
    return is_trading

def test_sector_lists(token):
    """测试版块列表获取"""
    print("\n" + "=" * 50)
    print("测试3: 版块列表获取")
    print("=" * 50)
    
    if not token:
        print("⚠️ 跳过版块列表测试（token未配置）")
        return False
    
    success_count = 0
    
    # 测试行业版块列表
    print("🔄 测试行业版块列表...")
    industry_df = get_tpdog_sector_list('bki', token)
    if industry_df is not None and not industry_df.empty:
        print(f"✅ 行业版块列表获取成功: {len(industry_df)} 个版块")
        print("前5个行业版块:")
        for idx, row in industry_df.head(5).iterrows():
            print(f"  {idx+1}. {row['name']} ({row['code']})")
        success_count += 1
    else:
        print("❌ 行业版块列表获取失败")
    
    # 测试概念版块列表
    print("\n🔄 测试概念版块列表...")
    concept_df = get_tpdog_sector_list('bkc', token)
    if concept_df is not None and not concept_df.empty:
        print(f"✅ 概念版块列表获取成功: {len(concept_df)} 个版块")
        print("前5个概念版块:")
        for idx, row in concept_df.head(5).iterrows():
            print(f"  {idx+1}. {row['name']} ({row['code']})")
        success_count += 1
    else:
        print("❌ 概念版块列表获取失败")
    
    return success_count == 2

def test_sector_funds(token, is_trading):
    """测试版块资金流获取"""
    print("\n" + "=" * 50)
    print("测试4: 版块资金流获取")
    print("=" * 50)
    
    if not token:
        print("⚠️ 跳过版块资金流测试（token未配置）")
        return False
    
    if not is_trading:
        print("⚠️ 非交易时间，版块资金流接口可能返回空数据")
    
    success_count = 0
    
    # 测试行业版块资金流
    print("🔄 测试行业版块资金流...")
    industry_funds_df = get_tpdog_sector_funds('bki', token)
    if industry_funds_df is not None and not industry_funds_df.empty:
        print(f"✅ 行业版块资金流获取成功: {len(industry_funds_df)} 条数据")
        print("前5名行业版块资金流:")
        for idx, row in industry_funds_df.head(5).iterrows():
            print(f"  {idx+1}. {row['name']:15s} | 主力净流入: {format_amount(row['m_net']):>10s}")
        success_count += 1
    else:
        print("❌ 行业版块资金流获取失败或数据为空")
    
    # 测试概念版块资金流
    print("\n🔄 测试概念版块资金流...")
    concept_funds_df = get_tpdog_sector_funds('bkc', token)
    if concept_funds_df is not None and not concept_funds_df.empty:
        print(f"✅ 概念版块资金流获取成功: {len(concept_funds_df)} 条数据")
        print("前5名概念版块资金流:")
        for idx, row in concept_funds_df.head(5).iterrows():
            print(f"  {idx+1}. {row['name']:15s} | 主力净流入: {format_amount(row['m_net']):>10s}")
        success_count += 1
    else:
        print("❌ 概念版块资金流获取失败或数据为空")
    
    return success_count > 0

def test_sector_stocks(token):
    """测试版块个股获取"""
    print("\n" + "=" * 50)
    print("测试5: 版块个股获取")
    print("=" * 50)
    
    if not token:
        print("⚠️ 跳过版块个股测试（token未配置）")
        return False
    
    # 测试一个固定的行业版块（软件开发）
    test_sector_code = "bki.880158"  # 软件开发
    print(f"🔄 测试版块个股获取 ({test_sector_code})...")
    
    stocks_df = get_tpdog_sector_stocks(test_sector_code, token)
    if stocks_df is not None and not stocks_df.empty:
        print(f"✅ 版块个股获取成功: {len(stocks_df)} 只股票")
        print("前5只股票:")
        for idx, row in stocks_df.head(5).iterrows():
            print(f"  {idx+1}. {row['name']} ({row['code']})")
        return True
    else:
        print("❌ 版块个股获取失败")
        return False

def test_sector_quotes(token):
    """测试版块行情获取"""
    print("\n" + "=" * 50)
    print("测试6: 版块行情获取")
    print("=" * 50)
    
    if not token:
        print("⚠️ 跳过版块行情测试（token未配置）")
        return False
    
    # 测试一个固定的行业版块（软件开发）
    test_sector_code = "bki.880158"  # 软件开发
    print(f"🔄 测试版块行情获取 ({test_sector_code})...")
    
    quote_data = get_tpdog_sector_quote(test_sector_code, token)
    if quote_data:
        print("✅ 版块行情获取成功")
        print(f"版块名称: {quote_data.get('name', 'N/A')}")
        print(f"最新价格: {quote_data.get('price', 'N/A')}")
        print(f"涨跌幅: {quote_data.get('raise_rate', 'N/A')}%")
        print(f"成交金额: {format_amount(quote_data.get('turnover', 0))}")
        return True
    else:
        print("❌ 版块行情获取失败")
        return False

def test_data_files():
    """测试数据文件保存"""
    print("\n" + "=" * 50)
    print("测试7: 数据文件检查")
    print("=" * 50)
    
    today_str = datetime.now().strftime('%Y%m%d')
    data_folder = os.path.join('data', today_str)
    
    if not os.path.exists(data_folder):
        print(f"⚠️ 今日数据文件夹不存在: {data_folder}")
        return False
    
    print(f"📁 检查数据文件夹: {data_folder}")
    
    # 查找TPDOG相关文件
    files = os.listdir(data_folder)
    tpdog_files = [f for f in files if 'tpdog' in f.lower()]
    
    if tpdog_files:
        print(f"✅ 找到 {len(tpdog_files)} 个TPDOG数据文件:")
        for file in tpdog_files[:10]:  # 只显示前10个
            file_path = os.path.join(data_folder, file)
            try:
                if file.endswith('.csv'):
                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                    print(f"  📄 {file}: {len(df)} 条记录")
                else:
                    print(f"  📄 {file}: 配置文件")
            except Exception as e:
                print(f"  ❌ {file}: 读取失败 - {e}")
        return True
    else:
        print("⚠️ 未找到TPDOG数据文件")
        return False

def main():
    """主测试函数"""
    print("开始测试TPDOG版块监控程序...")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 测试1: 环境配置
    token = test_env_config()
    
    # 测试2: 交易时间判断
    is_trading = test_trading_time()
    
    # 测试3: 版块列表获取
    lists_success = test_sector_lists(token)
    
    # 测试4: 版块资金流获取
    funds_success = test_sector_funds(token, is_trading)
    
    # 测试5: 版块个股获取
    stocks_success = test_sector_stocks(token)
    
    # 测试6: 版块行情获取
    quotes_success = test_sector_quotes(token)
    
    # 测试7: 数据文件检查
    files_success = test_data_files()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"{'✅' if token else '❌'} 环境配置: {'成功' if token else '失败'}")
    print(f"✅ 交易时间判断: 成功")
    print(f"{'✅' if lists_success else '❌'} 版块列表获取: {'成功' if lists_success else '失败'}")
    print(f"{'✅' if funds_success else '⚠️'} 版块资金流获取: {'成功' if funds_success else '失败或无数据'}")
    print(f"{'✅' if stocks_success else '❌'} 版块个股获取: {'成功' if stocks_success else '失败'}")
    print(f"{'✅' if quotes_success else '❌'} 版块行情获取: {'成功' if quotes_success else '失败'}")
    print(f"{'✅' if files_success else '⚠️'} 数据文件检查: {'成功' if files_success else '无文件'}")
    
    success_count = sum([bool(token), True, lists_success, funds_success, stocks_success, quotes_success, files_success])
    total_tests = 7
    
    if success_count >= 5:
        print(f"\n🎉 TPDOG版块监控程序测试通过！({success_count}/{total_tests})")
        print("程序可以正常运行，建议启动监控程序。")
    else:
        print(f"\n⚠️ 部分功能需要检查 ({success_count}/{total_tests})")
        print("请检查TPDOG_TOKEN配置和网络连接。")

if __name__ == "__main__":
    main()
