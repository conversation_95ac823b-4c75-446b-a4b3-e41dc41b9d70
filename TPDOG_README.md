# TPDOG版块资金流监控程序

专门用于获取TPDOG平台的概念、行业版块资金流数据的监控程序。

## 功能特性

### 🎯 核心功能
1. **版块列表管理**: 收盘时自动检查并获取概念版块和行业版块列表
2. **实时资金流监控**: 交易时间每2分钟获取版块资金流数据
3. **前10版块分析**: 自动获取资金流前10版块的个股数据
4. **实时行情数据**: 获取版块实时行情信息（涨跌幅、成交额等）
5. **数据持久化**: 所有数据自动保存为CSV格式，便于后续分析

### ⏰ 运行时间
- **版块列表获取**: 每日15:10（收盘后）
- **资金流监控**: 交易时间09:30-11:30 / 13:00-15:00，每2分钟执行一次
- **非交易时间**: 程序待机，不执行数据获取

### 📊 数据类型
- **行业版块资金流**: 86个行业版块的主力资金流入流出数据
- **概念版块资金流**: 474个概念版块的主力资金流入流出数据
- **版块个股列表**: 前10版块内的所有个股信息
- **版块实时行情**: 版块价格、涨跌幅、成交额等行情数据

## 安装配置

### 1. 环境要求
```bash
Python 3.7+
```

### 2. 安装依赖
```bash
pip install requests pandas schedule python-dotenv
```

### 3. 配置TPDOG Token
创建`.env`文件并配置：
```env
TPDOG_TOKEN=your_tpdog_token_here
```

## 使用方法

### 方式一：使用启动器（推荐）
```bash
python start_tpdog_monitor.py
```

启动器提供以下选项：
1. **运行测试程序** - 验证所有功能是否正常
2. **启动监控程序** - 正式运行监控程序
3. **手动获取版块列表** - 立即获取版块列表
4. **手动获取版块资金流** - 立即获取资金流数据
5. **查看今日数据文件** - 查看已保存的数据文件

### 方式二：直接运行
```bash
# 运行测试
python test_tpdog_monitor.py

# 启动监控
python tpdog_sector_monitor.py
```

## 数据文件结构

程序会在`data/YYYYMMDD/`目录下保存数据：

```
data/
└── 20250727/
    ├── tpdog_monitor.log                           # 运行日志
    ├── tpdog_industry_list.json                    # 行业版块列表(JSON)
    ├── tpdog_industry_list_20250727_151000.csv     # 行业版块列表(CSV)
    ├── tpdog_concept_list.json                     # 概念版块列表(JSON)
    ├── tpdog_concept_list_20250727_151000.csv      # 概念版块列表(CSV)
    ├── tpdog_industry_funds_20250727_093000.csv    # 行业资金流数据
    ├── tpdog_concept_funds_20250727_093000.csv     # 概念资金流数据
    ├── tpdog_industry_stocks_软件开发_093000.csv    # 行业个股数据
    ├── tpdog_concept_stocks_人工智能_093000.csv     # 概念个股数据
    └── tpdog_sectors_quotes_20250727_093000.csv    # 版块行情数据
```

### 文件说明
- **版块列表文件**: 包含版块代码、名称等基础信息
- **资金流文件**: 包含主力净流入、净流入比例等资金流数据
- **个股文件**: 包含版块内所有个股的代码、名称等信息
- **行情文件**: 包含版块价格、涨跌幅、成交额等实时行情

## API接口说明

### TPDOG API接口
程序使用以下TPDOG API接口：

1. **版块列表**: `/api/bk/list?type={bki|bkc}&token=`
   - `bki`: 行业版块
   - `bkc`: 概念版块

2. **版块资金流**: `/api/hs/current/bk_funds?bk_type={bki|bkc}&token=`
   - 仅在交易时间有效
   - 按主力净流入排序

3. **版块个股**: `/api/hs/stocks/list_board?code={sector_code}&token=`
   - 获取指定版块内的所有个股

4. **版块行情**: `/api/hs/current/inventory?code={sector_code}&token=`
   - 获取版块实时行情数据
   - 非交易时间返回示例数据

## 运行示例

### 测试输出示例
```
==================================================
测试1: 环境配置检查
==================================================
✅ TPDOG_TOKEN已配置: ecc4236bd2...

==================================================
测试3: 版块列表获取
==================================================
✅ 行业版块列表获取成功: 86 个版块
前5个行业版块:
  1. 游戏 (880104)
  2. 房地产服务 (880109)
  3. 生物制品 (880114)
  4. 专业服务 (880115)
  5. 医药商业 (880117)

✅ 概念版块列表获取成功: 474 个版块
前5个概念版块:
  1. 财税数字化 (880363)
  2. 玻璃基板 (880368)
  3. 合成生物 (880371)
  4. 锂矿概念 (880372)
  5. AI语料 (880373)
```

### 监控输出示例
```
==================================================
📊 开始获取TPDOG版块资金流数据
==================================================

📈 行业版块资金流前10名:
   1. 软件开发        | 主力净流入:    15.23亿 | 净流入比例:  2.45%
   2. 半导体          | 主力净流入:    12.87亿 | 净流入比例:  1.98%
   3. 新能源车        | 主力净流入:     8.56亿 | 净流入比例:  1.23%

📈 概念版块资金流前10名:
   1. 人工智能        | 主力净流入:    25.67亿 | 净流入比例:  3.21%
   2. 芯片概念        | 主力净流入:    18.45亿 | 净流入比例:  2.87%
   3. 新能源          | 主力净流入:    14.32亿 | 净流入比例:  2.15%
```

## 注意事项

### ⚠️ 重要提醒
1. **Token配置**: 必须正确配置TPDOG_TOKEN才能使用
2. **交易时间**: 资金流数据仅在交易时间有效
3. **请求频率**: 程序已内置请求间隔，避免过于频繁的API调用
4. **数据存储**: 数据文件较多，注意磁盘空间

### 🔧 故障排除
1. **Token错误**: 检查.env文件中的TPDOG_TOKEN配置
2. **网络问题**: 确保网络连接正常，可访问TPDOG API
3. **权限问题**: 确保程序有创建data目录和文件的权限
4. **依赖缺失**: 运行`pip install -r requirements.txt`安装依赖

## 技术特点

### 🛡️ 错误处理
- 完善的异常捕获和日志记录
- API失败时的优雅降级
- 非交易时间的智能跳过

### 📈 性能优化
- 合理的请求间隔避免API限制
- 数据缓存减少重复请求
- 多线程任务执行

### 💾 数据管理
- 按日期组织的文件结构
- CSV和JSON双格式保存
- 完整的数据追溯能力

## 扩展功能

程序设计为模块化结构，可以轻松扩展：
- 添加更多版块类型（地域版块等）
- 集成其他数据源
- 添加数据分析功能
- 实现数据可视化

## 联系支持

如有问题或建议，请检查：
1. 日志文件中的错误信息
2. TPDOG API文档
3. 网络连接状态
4. Token有效性
